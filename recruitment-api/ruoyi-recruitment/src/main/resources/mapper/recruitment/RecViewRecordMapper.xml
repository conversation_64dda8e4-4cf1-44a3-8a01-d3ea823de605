<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.recruitment.mapper.RecViewRecordMapper">
    
    <resultMap type="RecViewRecord" id="RecViewRecordResult">
        <result property="recordId"    column="record_id"    />
        <result property="seekerId"    column="seeker_id"    />
        <result property="positionId"    column="position_id"    />
        <result property="employerId"    column="employer_id"    />
        <result property="viewType"    column="view_type"    />
        <result property="costAmount"    column="cost_amount"    />
        <result property="orderId"    column="order_id"    />
        <result property="viewTime"    column="view_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectRecViewRecordVo">
        select record_id, seeker_id, position_id, employer_id, view_type, cost_amount, order_id, view_time, create_by, create_time from rec_view_record
    </sql>

    <select id="selectRecViewRecordList" parameterType="RecViewRecord" resultMap="RecViewRecordResult">
        <include refid="selectRecViewRecordVo"/>
        <where>  
            <if test="seekerId != null "> and seeker_id = #{seekerId}</if>
            <if test="positionId != null "> and position_id = #{positionId}</if>
            <if test="employerId != null "> and employer_id = #{employerId}</if>
            <if test="viewType != null "> and view_type = #{viewType}</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="params != null and params.beginViewTime != null and params.beginViewTime != ''"><!-- 开始时间检索 -->
                and date_format(view_time,'%y%m%d') &gt;= date_format(#{params.beginViewTime},'%y%m%d')
            </if>
            <if test="params != null and params.endViewTime != null and params.endViewTime != ''"><!-- 结束时间检索 -->
                and date_format(view_time,'%y%m%d') &lt;= date_format(#{params.endViewTime},'%y%m%d')
            </if>
        </where>
        order by view_time desc
    </select>
    
    <select id="selectRecViewRecordByRecordId" parameterType="Long" resultMap="RecViewRecordResult">
        <include refid="selectRecViewRecordVo"/>
        where record_id = #{recordId}
    </select>

    <select id="selectViewRecordsBySeekerId" parameterType="Long" resultType="java.util.Map">
        select 
            r.record_id,
            r.view_time,
            r.cost_amount,
            r.view_type,
            p.position_title,
            p.salary_min,
            p.salary_max,
            p.work_address,
            e.company_name,
            e.contact_person,
            e.contact_phone
        from rec_view_record r
        left join rec_job_position p on r.position_id = p.position_id
        left join rec_employer e on r.employer_id = e.employer_id
        where r.seeker_id = #{seekerId}
        order by r.view_time desc
    </select>

    <select id="selectViewRecordsByPositionId" parameterType="Long" resultMap="RecViewRecordResult">
        <include refid="selectRecViewRecordVo"/>
        where position_id = #{positionId}
        order by view_time desc
    </select>

    <select id="selectViewRecordsByEmployerId" parameterType="Long" resultMap="RecViewRecordResult">
        <include refid="selectRecViewRecordVo"/>
        where employer_id = #{employerId}
        order by view_time desc
    </select>

    <select id="countViewRecord" resultType="int">
        select count(*) from rec_view_record 
        where seeker_id = #{seekerId} and position_id = #{positionId}
    </select>

    <insert id="insertRecViewRecord" parameterType="RecViewRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into rec_view_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seekerId != null">seeker_id,</if>
            <if test="positionId != null">position_id,</if>
            <if test="employerId != null">employer_id,</if>
            <if test="viewType != null">view_type,</if>
            <if test="costAmount != null">cost_amount,</if>
            <if test="orderId != null">order_id,</if>
            <if test="viewTime != null">view_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seekerId != null">#{seekerId},</if>
            <if test="positionId != null">#{positionId},</if>
            <if test="employerId != null">#{employerId},</if>
            <if test="viewType != null">#{viewType},</if>
            <if test="costAmount != null">#{costAmount},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="viewTime != null">#{viewTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRecViewRecord" parameterType="RecViewRecord">
        update rec_view_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="seekerId != null">seeker_id = #{seekerId},</if>
            <if test="positionId != null">position_id = #{positionId},</if>
            <if test="employerId != null">employer_id = #{employerId},</if>
            <if test="viewType != null">view_type = #{viewType},</if>
            <if test="costAmount != null">cost_amount = #{costAmount},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="viewTime != null">view_time = #{viewTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteRecViewRecordByRecordId" parameterType="Long">
        delete from rec_view_record where record_id = #{recordId}
    </delete>

    <delete id="deleteRecViewRecordByRecordIds" parameterType="String">
        delete from rec_view_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

    <!-- 统计查询 -->
    <select id="countTotalViews" resultType="int">
        select count(*) from rec_view_record
    </select>

    <select id="countTodayViews" resultType="int">
        select count(*) from rec_view_record where date_format(view_time,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
    </select>

    <select id="countPaidViews" resultType="int">
        select count(*) from rec_view_record where view_type = 1
    </select>

    <select id="countMemberViews" resultType="int">
        select count(*) from rec_view_record where view_type = 2
    </select>

    <select id="sumTotalRevenue" resultType="java.math.BigDecimal">
        select IFNULL(sum(cost_amount), 0) from rec_view_record
    </select>

    <select id="sumTodayRevenue" resultType="java.math.BigDecimal">
        select IFNULL(sum(cost_amount), 0) from rec_view_record where date_format(view_time,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
    </select>

    <select id="getHotPositionViewStats" parameterType="Integer" resultType="java.util.Map">
        select 
            p.position_id,
            p.position_title,
            e.company_name,
            count(*) as view_count,
            sum(r.cost_amount) as total_revenue
        from rec_view_record r
        left join rec_job_position p on r.position_id = p.position_id
        left join rec_employer e on r.employer_id = e.employer_id
        group by r.position_id
        order by view_count desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="countUserTotalViews" parameterType="Long" resultType="int">
        select count(*) from rec_view_record where seeker_id = #{seekerId}
    </select>

    <select id="countUserPaidViews" parameterType="Long" resultType="int">
        select count(*) from rec_view_record where seeker_id = #{seekerId} and view_type = 1
    </select>

    <select id="countUserMemberViews" parameterType="Long" resultType="int">
        select count(*) from rec_view_record where seeker_id = #{seekerId} and view_type = 2
    </select>

    <select id="sumUserTotalCost" parameterType="Long" resultType="java.math.BigDecimal">
        select IFNULL(sum(cost_amount), 0) from rec_view_record where seeker_id = #{seekerId}
    </select>

</mapper>
