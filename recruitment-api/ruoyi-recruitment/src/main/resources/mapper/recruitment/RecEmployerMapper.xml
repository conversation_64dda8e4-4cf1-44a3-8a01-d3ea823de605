<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.recruitment.mapper.RecEmployerMapper">
    
    <resultMap type="RecEmployer" id="RecEmployerResult">
        <result property="employerId"    column="employer_id"    />
        <result property="companyName"    column="company_name"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="email"    column="email"    />
        <result property="businessLicense"    column="business_license"    />
        <result property="licenseImage"    column="license_image"    />
        <result property="companyAddress"    column="company_address"    />
        <result property="regionCode"    column="region_code"    />
        <result property="companyScale"    column="company_scale"    />
        <result property="companyNature"    column="company_nature"    />
        <result property="companyDescription"    column="company_description"    />
        <result property="logoUrl"    column="logo_url"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditBy"    column="audit_by"    />
        <result property="status"    column="status"    />
        <result property="loginAccount"    column="login_account"    />
        <result property="loginPassword"    column="login_password"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="regionName"    column="region_name"    />
    </resultMap>

    <sql id="selectRecEmployerVo">
        select e.employer_id, e.company_name, e.contact_person, e.contact_phone, e.email, 
               e.business_license, e.license_image, e.company_address, e.region_code, 
               e.company_scale, e.company_nature, e.company_description, e.logo_url, 
               e.audit_status, e.audit_remark, e.audit_time, e.audit_by, e.status, 
               e.login_account, e.login_password, e.create_by, e.create_time, 
               e.update_by, e.update_time, r.region_name
        from rec_employer e
        left join rec_region r on e.region_code = r.region_code
    </sql>

    <select id="selectRecEmployerList" parameterType="RecEmployer" resultMap="RecEmployerResult">
        <include refid="selectRecEmployerVo"/>
        <where>  
            <if test="companyName != null  and companyName != ''"> and e.company_name like concat('%', #{companyName}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and e.contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and e.contact_phone = #{contactPhone}</if>
            <if test="email != null  and email != ''"> and e.email = #{email}</if>
            <if test="regionCode != null  and regionCode != ''"> and e.region_code = #{regionCode}</if>
            <if test="auditStatus != null "> and e.audit_status = #{auditStatus}</if>
            <if test="status != null  and status != ''"> and e.status = #{status}</if>
            <if test="loginAccount != null  and loginAccount != ''"> and e.login_account = #{loginAccount}</if>
            <if test="params != null and params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始创建时间 -->
                and date_format(e.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params != null and params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束创建时间 -->
                and date_format(e.create_time,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
        order by e.create_time desc
    </select>
    
    <select id="selectRecEmployerByEmployerId" parameterType="Long" resultMap="RecEmployerResult">
        <include refid="selectRecEmployerVo"/>
        where e.employer_id = #{employerId}
    </select>

    <select id="selectRecEmployerByLoginAccount" parameterType="String" resultMap="RecEmployerResult">
        <include refid="selectRecEmployerVo"/>
        where e.login_account = #{loginAccount}
    </select>

    <select id="selectRecEmployerByContactPhone" parameterType="String" resultMap="RecEmployerResult">
        <include refid="selectRecEmployerVo"/>
        where e.contact_phone = #{contactPhone}
    </select>
        
    <insert id="insertRecEmployer" parameterType="RecEmployer" useGeneratedKeys="true" keyProperty="employerId">
        insert into rec_employer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="businessLicense != null and businessLicense != ''">business_license,</if>
            <if test="licenseImage != null and licenseImage != ''">license_image,</if>
            <if test="companyAddress != null and companyAddress != ''">company_address,</if>
            <if test="regionCode != null and regionCode != ''">region_code,</if>
            <if test="companyScale != null and companyScale != ''">company_scale,</if>
            <if test="companyNature != null and companyNature != ''">company_nature,</if>
            <if test="companyDescription != null and companyDescription != ''">company_description,</if>
            <if test="logoUrl != null and logoUrl != ''">logo_url,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditRemark != null and auditRemark != ''">audit_remark,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditBy != null and auditBy != ''">audit_by,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="loginAccount != null and loginAccount != ''">login_account,</if>
            <if test="loginPassword != null and loginPassword != ''">login_password,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="contactPerson != null and contactPerson != ''">#{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="businessLicense != null and businessLicense != ''">#{businessLicense},</if>
            <if test="licenseImage != null and licenseImage != ''">#{licenseImage},</if>
            <if test="companyAddress != null and companyAddress != ''">#{companyAddress},</if>
            <if test="regionCode != null and regionCode != ''">#{regionCode},</if>
            <if test="companyScale != null and companyScale != ''">#{companyScale},</if>
            <if test="companyNature != null and companyNature != ''">#{companyNature},</if>
            <if test="companyDescription != null and companyDescription != ''">#{companyDescription},</if>
            <if test="logoUrl != null and logoUrl != ''">#{logoUrl},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditRemark != null and auditRemark != ''">#{auditRemark},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditBy != null and auditBy != ''">#{auditBy},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="loginAccount != null and loginAccount != ''">#{loginAccount},</if>
            <if test="loginPassword != null and loginPassword != ''">#{loginPassword},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRecEmployer" parameterType="RecEmployer">
        update rec_employer
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="businessLicense != null and businessLicense != ''">business_license = #{businessLicense},</if>
            <if test="licenseImage != null and licenseImage != ''">license_image = #{licenseImage},</if>
            <if test="companyAddress != null and companyAddress != ''">company_address = #{companyAddress},</if>
            <if test="regionCode != null and regionCode != ''">region_code = #{regionCode},</if>
            <if test="companyScale != null and companyScale != ''">company_scale = #{companyScale},</if>
            <if test="companyNature != null and companyNature != ''">company_nature = #{companyNature},</if>
            <if test="companyDescription != null and companyDescription != ''">company_description = #{companyDescription},</if>
            <if test="logoUrl != null and logoUrl != ''">logo_url = #{logoUrl},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditRemark != null and auditRemark != ''">audit_remark = #{auditRemark},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditBy != null and auditBy != ''">audit_by = #{auditBy},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="loginAccount != null and loginAccount != ''">login_account = #{loginAccount},</if>
            <if test="loginPassword != null and loginPassword != ''">login_password = #{loginPassword},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where employer_id = #{employerId}
    </update>

    <delete id="deleteRecEmployerByEmployerId" parameterType="Long">
        delete from rec_employer where employer_id = #{employerId}
    </delete>

    <delete id="deleteRecEmployerByEmployerIds" parameterType="String">
        delete from rec_employer where employer_id in 
        <foreach item="employerId" collection="array" open="(" separator="," close=")">
            #{employerId}
        </foreach>
    </delete>

    <update id="updateEmployerAuditStatus" parameterType="RecEmployer">
        update rec_employer
        set audit_status = #{auditStatus},
            audit_remark = #{auditRemark},
            audit_time = #{auditTime},
            audit_by = #{auditBy},
            update_time = #{updateTime}
        where employer_id = #{employerId}
    </update>

    <select id="countTotalEmployers" resultType="int">
        select count(*) from rec_employer where status = '0'
    </select>

    <select id="countPendingAuditEmployers" resultType="int">
        select count(*) from rec_employer where audit_status = 0 and status = '0'
    </select>

    <select id="countApprovedEmployers" resultType="int">
        select count(*) from rec_employer where audit_status = 1 and status = '0'
    </select>

    <select id="countTodayNewEmployers" resultType="int">
        select count(*) from rec_employer 
        where date_format(create_time,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
    </select>

    <select id="selectPendingAuditEmployers" resultMap="RecEmployerResult">
        <include refid="selectRecEmployerVo"/>
        where e.audit_status = 0 and e.status = '0'
        order by e.create_time asc
    </select>

</mapper>
