package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.domain.RecJobPosition;
import com.ruoyi.recruitment.service.IRecJobPositionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 职位Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/recruitment/position")
public class RecJobPositionController extends BaseController
{
    @Autowired
    private IRecJobPositionService recJobPositionService;

    /**
     * 查询职位列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecJobPosition recJobPosition)
    {
        startPage();
        List<RecJobPosition> list = recJobPositionService.selectRecJobPositionList(recJobPosition);
        return getDataTable(list);
    }

    /**
     * 导出职位列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:export')")
    @Log(title = "职位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecJobPosition recJobPosition)
    {
        List<RecJobPosition> list = recJobPositionService.selectRecJobPositionList(recJobPosition);
        ExcelUtil<RecJobPosition> util = new ExcelUtil<RecJobPosition>(RecJobPosition.class);
        util.exportExcel(response, list, "职位数据");
    }

    /**
     * 获取职位详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:query')")
    @GetMapping(value = "/{positionId}")
    public AjaxResult getInfo(@PathVariable("positionId") Long positionId)
    {
        return success(recJobPositionService.selectRecJobPositionByPositionId(positionId));
    }

    /**
     * 新增职位
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:add')")
    @Log(title = "职位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecJobPosition recJobPosition)
    {
        return toAjax(recJobPositionService.insertRecJobPosition(recJobPosition));
    }

    /**
     * 修改职位
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:edit')")
    @Log(title = "职位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecJobPosition recJobPosition)
    {
        return toAjax(recJobPositionService.updateRecJobPosition(recJobPosition));
    }

    /**
     * 删除职位
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:remove')")
    @Log(title = "职位", businessType = BusinessType.DELETE)
	@DeleteMapping("/{positionIds}")
    public AjaxResult remove(@PathVariable Long[] positionIds)
    {
        return toAjax(recJobPositionService.deleteRecJobPositionByPositionIds(positionIds));
    }

    /**
     * 审核职位
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:audit')")
    @Log(title = "职位审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody RecJobPosition recJobPosition)
    {
        return toAjax(recJobPositionService.auditPosition(
            recJobPosition.getPositionId(), 
            recJobPosition.getAuditStatus(), 
            recJobPosition.getAuditRemark()
        ));
    }

    /**
     * 批量审核职位
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:audit')")
    @Log(title = "批量审核职位", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAudit")
    public AjaxResult batchAudit(@RequestBody RecJobPosition recJobPosition, @RequestParam Long[] positionIds)
    {
        return toAjax(recJobPositionService.batchAuditPositions(
            positionIds, 
            recJobPosition.getAuditStatus(), 
            recJobPosition.getAuditRemark()
        ));
    }

    /**
     * 上线/下线职位
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:edit')")
    @Log(title = "职位状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{positionId}/{publishStatus}")
    public AjaxResult updateStatus(@PathVariable Long positionId, @PathVariable Integer publishStatus)
    {
        return toAjax(recJobPositionService.updatePositionStatus(positionId, publishStatus));
    }

    /**
     * 批量上线/下线职位
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:edit')")
    @Log(title = "批量更新职位状态", businessType = BusinessType.UPDATE)
    @PutMapping("/batchStatus")
    public AjaxResult batchUpdateStatus(@RequestParam Long[] positionIds, @RequestParam Integer publishStatus)
    {
        return toAjax(recJobPositionService.batchUpdatePositionStatus(positionIds, publishStatus));
    }

    /**
     * 置顶/取消置顶职位
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:edit')")
    @Log(title = "职位置顶", businessType = BusinessType.UPDATE)
    @PutMapping("/top/{positionId}/{isTop}")
    public AjaxResult updateTop(@PathVariable Long positionId, @PathVariable Integer isTop)
    {
        return toAjax(recJobPositionService.updatePositionTop(positionId, isTop));
    }

    /**
     * 设置紧急职位
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:edit')")
    @Log(title = "紧急职位", businessType = BusinessType.UPDATE)
    @PutMapping("/urgent/{positionId}/{isUrgent}")
    public AjaxResult updateUrgent(@PathVariable Long positionId, @PathVariable Integer isUrgent)
    {
        return toAjax(recJobPositionService.updatePositionUrgent(positionId, isUrgent));
    }

    /**
     * 获取职位统计信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        return success(recJobPositionService.getPositionStatistics());
    }

    /**
     * 查询待审核职位列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:audit')")
    @GetMapping("/pendingAudit")
    public AjaxResult getPendingAuditList()
    {
        List<RecJobPosition> list = recJobPositionService.selectPendingAuditPositions();
        return success(list);
    }

    /**
     * 查询热门职位列表
     */
    @GetMapping("/hot")
    public AjaxResult getHotPositions(@RequestParam(defaultValue = "10") Integer limit)
    {
        List<RecJobPosition> list = recJobPositionService.selectHotPositions(limit);
        return success(list);
    }



    /**
     * 根据地区查询职位列表
     */
    @GetMapping("/region/{regionCode}")
    public AjaxResult getPositionsByRegion(@PathVariable String regionCode)
    {
        List<RecJobPosition> list = recJobPositionService.selectPositionsByRegion(regionCode);
        return success(list);
    }

    /**
     * 搜索职位
     */
    @GetMapping("/search")
    public AjaxResult searchPositions(@RequestParam String keyword, 
                                     @RequestParam(required = false) String regionCode)
    {
        List<RecJobPosition> list = recJobPositionService.searchPositions(keyword, regionCode);
        return success(list);
    }

    /**
     * 根据商家ID查询职位列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:position:list')")
    @GetMapping("/employer/{employerId}")
    public AjaxResult getPositionsByEmployer(@PathVariable Long employerId)
    {
        List<RecJobPosition> list = recJobPositionService.selectRecJobPositionByEmployerId(employerId);
        return success(list);
    }
}
