package com.ruoyi.recruitment.utils;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.recruitment.domain.RecJobSeeker;
import com.ruoyi.recruitment.service.IRecJobSeekerService;
import com.ruoyi.common.utils.spring.SpringUtils;

/**
 * 小程序安全工具类
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public class MiniAppSecurityUtils
{
    /**
     * 获取当前登录的小程序用户ID
     *
     * @return 用户ID
     */
    public static Long getCurrentSeekerId()
    {
        try
        {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null && loginUser.getUsername().startsWith("miniapp_"))
            {
                return loginUser.getUserId();
            }
        }
        catch (Exception e)
        {
            // 未登录或获取失败
        }
        return null;
    }

    /**
     * 获取当前登录的小程序用户信息
     *
     * @return 当前用户信息
     */
    public static RecJobSeeker getCurrentSeeker()
    {
        Long seekerId = getCurrentSeekerId();
        if (seekerId != null)
        {
            try
            {
                IRecJobSeekerService recJobSeekerService = SpringUtils.getBean(IRecJobSeekerService.class);
                return recJobSeekerService.selectRecJobSeekerBySeekerId(seekerId);
            }
            catch (Exception e)
            {
                // 获取失败
            }
        }
        return null;
    }

    /**
     * 检查当前用户是否已登录
     *
     * @return 是否已登录
     */
    public static boolean isAuthenticated()
    {
        return getCurrentSeekerId() != null;
    }

    /**
     * 检查当前用户是否为指定用户
     *
     * @param seekerId 用户ID
     * @return 是否为指定用户
     */
    public static boolean isCurrentUser(Long seekerId)
    {
        Long currentSeekerId = getCurrentSeekerId();
        return currentSeekerId != null && currentSeekerId.equals(seekerId);
    }
}
