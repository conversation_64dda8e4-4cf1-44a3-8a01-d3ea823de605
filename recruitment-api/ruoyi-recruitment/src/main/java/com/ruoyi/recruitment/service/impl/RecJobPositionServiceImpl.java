package com.ruoyi.recruitment.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.recruitment.mapper.RecJobPositionMapper;
import com.ruoyi.recruitment.domain.RecJobPosition;
import com.ruoyi.recruitment.service.IRecJobPositionService;
import com.ruoyi.recruitment.enums.AuditStatusEnum;
import com.ruoyi.recruitment.enums.PublishStatusEnum;

/**
 * 职位Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class RecJobPositionServiceImpl implements IRecJobPositionService 
{
    @Autowired
    private RecJobPositionMapper recJobPositionMapper;

    /**
     * 查询职位
     * 
     * @param positionId 职位主键
     * @return 职位
     */
    @Override
    public RecJobPosition selectRecJobPositionByPositionId(Long positionId)
    {
        return recJobPositionMapper.selectRecJobPositionByPositionId(positionId);
    }

    /**
     * 查询职位列表
     * 
     * @param recJobPosition 职位
     * @return 职位
     */
    @Override
    public List<RecJobPosition> selectRecJobPositionList(RecJobPosition recJobPosition)
    {
        return recJobPositionMapper.selectRecJobPositionList(recJobPosition);
    }

    /**
     * 根据商家ID查询职位列表
     * 
     * @param employerId 商家ID
     * @return 职位集合
     */
    @Override
    public List<RecJobPosition> selectRecJobPositionByEmployerId(Long employerId)
    {
        return recJobPositionMapper.selectRecJobPositionByEmployerId(employerId);
    }

    /**
     * 新增职位
     * 
     * @param recJobPosition 职位
     * @return 结果
     */
    @Override
    public int insertRecJobPosition(RecJobPosition recJobPosition)
    {
        recJobPosition.setCreateTime(DateUtils.getNowDate());
        return recJobPositionMapper.insertRecJobPosition(recJobPosition);
    }

    /**
     * 修改职位
     * 
     * @param recJobPosition 职位
     * @return 结果
     */
    @Override
    public int updateRecJobPosition(RecJobPosition recJobPosition)
    {
        recJobPosition.setUpdateTime(DateUtils.getNowDate());
        return recJobPositionMapper.updateRecJobPosition(recJobPosition);
    }

    /**
     * 批量删除职位
     * 
     * @param positionIds 需要删除的职位主键
     * @return 结果
     */
    @Override
    public int deleteRecJobPositionByPositionIds(Long[] positionIds)
    {
        return recJobPositionMapper.deleteRecJobPositionByPositionIds(positionIds);
    }

    /**
     * 删除职位信息
     * 
     * @param positionId 职位主键
     * @return 结果
     */
    @Override
    public int deleteRecJobPositionByPositionId(Long positionId)
    {
        return recJobPositionMapper.deleteRecJobPositionByPositionId(positionId);
    }

    /**
     * 发布职位
     * 
     * @param recJobPosition 职位信息
     * @return 结果
     */
    @Override
    public int publishPosition(RecJobPosition recJobPosition)
    {
        recJobPosition.setPublishStatus(PublishStatusEnum.PENDING_AUDIT.getCode());
        recJobPosition.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        recJobPosition.setUpdateTime(new Date());
        return recJobPositionMapper.updateRecJobPosition(recJobPosition);
    }

    /**
     * 审核职位
     * 
     * @param positionId 职位ID
     * @param auditStatus 审核状态
     * @param auditRemark 审核备注
     * @return 结果
     */
    @Override
    public int auditPosition(Long positionId, Integer auditStatus, String auditRemark)
    {
        RecJobPosition position = new RecJobPosition();
        position.setPositionId(positionId);
        position.setAuditStatus(auditStatus);
        position.setAuditRemark(auditRemark);
        position.setAuditTime(new Date());
        position.setAuditBy(SecurityUtils.getUsername());
        
        // 如果审核通过，自动发布
        if (AuditStatusEnum.APPROVED.getCode().equals(auditStatus)) {
            position.setPublishStatus(PublishStatusEnum.PUBLISHED.getCode());
            position.setPublishTime(new Date());
        }
        
        return recJobPositionMapper.updatePositionAuditStatus(position);
    }

    /**
     * 上线/下线职位
     * 
     * @param positionId 职位ID
     * @param publishStatus 发布状态
     * @return 结果
     */
    @Override
    public int updatePositionStatus(Long positionId, Integer publishStatus)
    {
        RecJobPosition position = new RecJobPosition();
        position.setPositionId(positionId);
        position.setPublishStatus(publishStatus);
        position.setUpdateTime(new Date());
        
        if (PublishStatusEnum.PUBLISHED.getCode().equals(publishStatus)) {
            position.setPublishTime(new Date());
        }
        
        return recJobPositionMapper.updatePositionPublishStatus(position);
    }

    /**
     * 置顶/取消置顶职位
     * 
     * @param positionId 职位ID
     * @param isTop 是否置顶
     * @return 结果
     */
    @Override
    public int updatePositionTop(Long positionId, Integer isTop)
    {
        RecJobPosition position = new RecJobPosition();
        position.setPositionId(positionId);
        position.setIsTop(isTop);
        position.setUpdateTime(new Date());
        return recJobPositionMapper.updateRecJobPosition(position);
    }

    /**
     * 设置紧急职位
     * 
     * @param positionId 职位ID
     * @param isUrgent 是否紧急
     * @return 结果
     */
    @Override
    public int updatePositionUrgent(Long positionId, Integer isUrgent)
    {
        RecJobPosition position = new RecJobPosition();
        position.setPositionId(positionId);
        position.setIsUrgent(isUrgent);
        position.setUpdateTime(new Date());
        return recJobPositionMapper.updateRecJobPosition(position);
    }

    /**
     * 增加职位浏览次数
     * 
     * @param positionId 职位ID
     * @return 结果
     */
    @Override
    public boolean increaseViewCount(Long positionId)
    {
        int result = recJobPositionMapper.increaseViewCount(positionId);
        return result > 0;
    }

    /**
     * 增加职位申请次数
     * 
     * @param positionId 职位ID
     * @return 结果
     */
    @Override
    public boolean increaseApplyCount(Long positionId)
    {
        int result = recJobPositionMapper.increaseApplyCount(positionId);
        return result > 0;
    }

    /**
     * 检查职位是否可以查看联系方式
     * 
     * @param positionId 职位ID
     * @return 是否可以查看
     */
    @Override
    public boolean canViewContact(Long positionId)
    {
        RecJobPosition position = recJobPositionMapper.selectRecJobPositionByPositionId(positionId);
        if (position == null) {
            return false;
        }
        
        // 检查职位是否已发布且审核通过
        return PublishStatusEnum.PUBLISHED.getCode().equals(position.getPublishStatus()) 
            && AuditStatusEnum.APPROVED.getCode().equals(position.getAuditStatus());
    }

    /**
     * 获取职位统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Object getPositionStatistics()
    {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalPositions", recJobPositionMapper.countTotalPositions());
        statistics.put("publishedPositions", recJobPositionMapper.countPublishedPositions());
        statistics.put("pendingAuditPositions", recJobPositionMapper.countPendingAuditPositions());
        statistics.put("todayNewPositions", recJobPositionMapper.countTodayNewPositions());
        return statistics;
    }

    /**
     * 查询待审核职位列表
     * 
     * @return 职位集合
     */
    @Override
    public List<RecJobPosition> selectPendingAuditPositions()
    {
        return recJobPositionMapper.selectPendingAuditPositions();
    }

    /**
     * 查询热门职位列表
     * 
     * @param limit 限制数量
     * @return 职位集合
     */
    @Override
    public List<RecJobPosition> selectHotPositions(Integer limit)
    {
        return recJobPositionMapper.selectHotPositions(limit);
    }



    /**
     * 根据地区查询职位列表
     * 
     * @param regionCode 地区编码
     * @return 职位集合
     */
    @Override
    public List<RecJobPosition> selectPositionsByRegion(String regionCode)
    {
        return recJobPositionMapper.selectPositionsByRegion(regionCode);
    }

    /**
     * 搜索职位
     * 
     * @param keyword 关键词
     * @param regionCode 地区编码
     * @return 职位集合
     */
    @Override
    public List<RecJobPosition> searchPositions(String keyword, String regionCode)
    {
        return recJobPositionMapper.searchPositions(keyword, regionCode);
    }

    /**
     * 批量审核职位
     * 
     * @param positionIds 职位ID数组
     * @param auditStatus 审核状态
     * @param auditRemark 审核备注
     * @return 结果
     */
    @Override
    public int batchAuditPositions(Long[] positionIds, Integer auditStatus, String auditRemark)
    {
        int count = 0;
        for (Long positionId : positionIds) {
            count += auditPosition(positionId, auditStatus, auditRemark);
        }
        return count;
    }

    /**
     * 批量上线/下线职位
     *
     * @param positionIds 职位ID数组
     * @param publishStatus 发布状态
     * @return 结果
     */
    @Override
    public int batchUpdatePositionStatus(Long[] positionIds, Integer publishStatus)
    {
        int count = 0;
        for (Long positionId : positionIds) {
            count += updatePositionStatus(positionId, publishStatus);
        }
        return count;
    }

    /**
     * 根据筛选条件查询职位列表
     *
     * @param params 筛选参数
     * @return 职位集合
     */
    @Override
    public List<RecJobPosition> selectPositionsWithFilters(Map<String, Object> params)
    {
        return recJobPositionMapper.selectPositionsWithFilters(params);
    }
}
