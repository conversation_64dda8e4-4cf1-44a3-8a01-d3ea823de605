package com.ruoyi.recruitment.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.recruitment.domain.RecJobSeeker;
import com.ruoyi.recruitment.service.*;
import com.ruoyi.recruitment.service.impl.WechatServiceImpl;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.domain.entity.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 小程序API Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Anonymous
@RestController
@RequestMapping("/miniapp/auth")
public class MiniAppLoginController extends BaseController
{
    @Autowired
    private IRecJobSeekerService recJobSeekerService;

    @Autowired
    private IWechatService wechatService;

    @Autowired
    private ISmsService smsService;

    @Autowired
    private TokenService tokenService;

    /**
     * 微信授权登录
     */
    @PostMapping("/wechat")
    public AjaxResult wechatLogin(@RequestBody Map<String, String> params)
    {
        String code = params.get("code");
        try {
            RecJobSeeker seeker = wechatService.wechatLogin(code);
            if (seeker != null) {
                // 生成token和返回数据
                String token = createTokenForSeeker(seeker);

                Map<String, Object> data = new HashMap<>();
                data.put("token", token);
                data.put("userInfo", seeker);

                return success(data);
            } else {
                return error("登录失败");
            }
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 手机号一键登录（微信授权方式）
     */
    @PostMapping("/phone/wechat")
    public AjaxResult phoneLoginByWechat(@RequestBody Map<String, String> params)
    {
        String encryptedData = params.get("encryptedData");
        String iv = params.get("iv");
        String sessionKey = params.get("sessionKey");

        try {
            RecJobSeeker seeker = ((WechatServiceImpl) wechatService).phoneLoginByWechat(encryptedData, iv, sessionKey);
            if (seeker != null) {
                // 生成token和返回数据
                String token = createTokenForSeeker(seeker);

                Map<String, Object> data = new HashMap<>();
                data.put("token", token);
                data.put("userInfo", seeker);

                return success(data);
            } else {
                return error("登录失败");
            }
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 手机号一键登录（验证码方式）
     */
    @PostMapping("/phone")
    public AjaxResult phoneLogin(@RequestBody Map<String, String> params)
    {
        String phone = params.get("phone");
        String code = params.get("code");
        try {
            RecJobSeeker seeker = wechatService.phoneLogin(phone, code);
            if (seeker != null) {
                // 生成token和返回数据
                String token = createTokenForSeeker(seeker);

                Map<String, Object> data = new HashMap<>();
                data.put("token", token);
                data.put("userInfo", seeker);

                return success(data);
            } else {
                return error("登录失败");
            }
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 发送短信验证码
     */
    @PostMapping("/sms/send")
    public AjaxResult sendSmsCode(@RequestBody Map<String, String> params)
    {
        String phone = params.get("phone");

        if (phone == null || phone.trim().isEmpty()) {
            return error("手机号不能为空");
        }

        // 简单的手机号格式验证
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            return error("手机号格式不正确");
        }

        try {
            boolean result = smsService.sendVerificationCode(phone);
            if (result) {
                return success("验证码发送成功");
            } else {
                return error("验证码发送失败，请稍后重试");
            }
        } catch (Exception e) {
            return error("验证码发送失败：" + e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody RecJobSeeker recJobSeeker)
    {
        try {
            // 检查手机号是否已存在
            if (recJobSeeker.getPhone() != null && !recJobSeeker.getPhone().isEmpty()) {
                RecJobSeeker existingSeeker = recJobSeekerService.selectRecJobSeekerByPhone(recJobSeeker.getPhone());
                if (existingSeeker != null) {
                    return error("手机号已被注册");
                }
            }

            // 注册用户
            int result = recJobSeekerService.registerSeeker(recJobSeeker);
            if (result > 0) {
                // 重新查询用户信息（获取生成的ID）
                RecJobSeeker newSeeker;
                if (recJobSeeker.getPhone() != null && !recJobSeeker.getPhone().isEmpty()) {
                    newSeeker = recJobSeekerService.selectRecJobSeekerByPhone(recJobSeeker.getPhone());
                } else {
                    return error("注册失败：缺少必要信息");
                }

                if (newSeeker != null) {
                    // 生成token和返回数据
                    String token = createTokenForSeeker(newSeeker);

                    Map<String, Object> data = new HashMap<>();
                    data.put("token", token);
                    data.put("userInfo", newSeeker);

                    return success(data);
                } else {
                    return error("注册失败");
                }
            } else {
                return error("注册失败");
            }
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 为小程序用户创建token（使用若依框架的TokenService）
     */
    private String createTokenForSeeker(RecJobSeeker seeker)
    {
        // 创建SysUser对象
        SysUser sysUser = new SysUser();
        sysUser.setUserId(seeker.getSeekerId());
        sysUser.setUserName("miniapp_" + seeker.getSeekerId()); // 小程序用户名前缀
        sysUser.setNickName(seeker.getNickname() != null ? seeker.getNickname() : "小程序用户");
        sysUser.setPhonenumber(seeker.getPhone());
        sysUser.setStatus("0"); // 正常状态

        // 创建权限集合（小程序用户基础权限）
        Set<String> permissions = new HashSet<>();
        permissions.add("miniapp:user"); // 小程序用户基础权限

        // 创建LoginUser对象
        LoginUser loginUser = new LoginUser(seeker.getSeekerId(), null, sysUser, permissions);

        // 使用若依框架的TokenService生成token
        return tokenService.createToken(loginUser);
    }

}
