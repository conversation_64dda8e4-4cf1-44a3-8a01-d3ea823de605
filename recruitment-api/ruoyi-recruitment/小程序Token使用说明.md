# 小程序Token使用说明

## 概述

本系统为小程序用户实现了与若依框架一致的token认证机制，使用若依框架的`TokenService`来生成和管理token，确保与系统其他部分的一致性。

## 主要特性

1. **统一的Token管理**: 使用若依框架的TokenService，与后台管理系统保持一致
2. **JWT Token**: 生成的token是标准的JWT格式，包含用户信息和权限
3. **自动过期管理**: Token有过期时间，支持自动刷新
4. **Redis缓存**: 用户信息缓存在Redis中，提高性能
5. **权限控制**: 小程序用户拥有基础权限，可扩展

## API接口

### 1. 微信授权登录
```
POST /miniapp/auth/wechat
Content-Type: application/json

{
    "code": "微信授权码"
}
```

### 2. 手机号一键登录（微信授权方式）
```
POST /miniapp/auth/phone/wechat
Content-Type: application/json

{
    "encryptedData": "加密数据",
    "iv": "初始向量",
    "sessionKey": "会话密钥"
}
```

### 3. 手机号验证码登录
```
POST /miniapp/auth/phone
Content-Type: application/json

{
    "phone": "手机号",
    "code": "验证码"
}
```

### 4. 发送短信验证码
```
POST /miniapp/auth/sms/send
Content-Type: application/json

{
    "phone": "手机号"
}
```

### 5. 用户注册
```
POST /miniapp/auth/register
Content-Type: application/json

{
    "phone": "手机号",
    "nickname": "昵称",
    "avatar": "头像URL",
    "gender": 1
}
```

## 返回格式

所有登录接口成功后都会返回以下格式：

```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "token": "eyJhbGciOiJIUzUxMiJ9...",
        "userInfo": {
            "seekerId": 1,
            "phone": "13800138000",
            "nickname": "用户昵称",
            "avatar": "头像URL",
            "memberType": 0,
            "status": "0"
        }
    }
}
```

## Token使用

### 1. 请求头方式（推荐）
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```

### 2. 请求头方式（简化）
```
token: eyJhbGciOiJIUzUxMiJ9...
```

### 3. 参数方式
```
GET /api/some-endpoint?token=eyJhbGciOiJIUzUxMiJ9...
```

## 工具类使用

在其他Controller中获取当前登录用户：

```java
import com.ruoyi.recruitment.utils.MiniAppSecurityUtils;

@RestController
@RequestMapping("/miniapp/api")
public class SomeController {
    
    @GetMapping("/profile")
    public AjaxResult getProfile() {
        // 获取当前登录用户ID
        Long currentSeekerId = MiniAppSecurityUtils.getCurrentSeekerId();
        
        // 获取当前登录用户完整信息
        RecJobSeeker currentSeeker = MiniAppSecurityUtils.getCurrentSeeker();
        
        // 检查是否已登录
        if (!MiniAppSecurityUtils.isAuthenticated()) {
            return error("请先登录");
        }
        
        // 检查是否为指定用户
        if (!MiniAppSecurityUtils.isCurrentUser(someSeekerId)) {
            return error("无权访问");
        }
        
        return success(currentSeeker);
    }
}
```

## Token配置

Token相关配置在`application.yml`中：

```yaml
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30
```

## 权限说明

小程序用户默认拥有以下权限：
- `miniapp:user`: 小程序用户基础权限

可以根据需要扩展更多权限，比如：
- `miniapp:vip`: VIP用户权限
- `miniapp:premium`: 高级用户权限

## 注意事项

1. **用户名格式**: 小程序用户的用户名格式为`miniapp_{seekerId}`
2. **权限验证**: 使用若依框架的权限验证机制，可以在方法上使用`@PreAuthorize`注解
3. **Token刷新**: Token会在剩余时间少于20分钟时自动刷新
4. **缓存管理**: 用户信息缓存在Redis中，默认过期时间为30分钟
5. **安全性**: 生产环境建议修改token密钥，并使用更复杂的密钥

## 扩展说明

如需要添加更多的token管理功能（如主动刷新、退出登录等），可以直接使用若依框架的TokenService：

```java
@Autowired
private TokenService tokenService;

// 删除token（退出登录）
tokenService.delLoginUser(token);

// 验证token
LoginUser loginUser = tokenService.getLoginUser(request);
```
