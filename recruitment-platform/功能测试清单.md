# 招聘平台功能测试清单

## 1. 后端API接口测试

### 1.1 职位列表接口 (/miniapp/positions)
- [ ] 基础查询功能
- [ ] 按发布时间排序
- [ ] 按薪资高低排序  
- [ ] 按距离远近排序（需要经纬度参数）
- [ ] 行业筛选功能
- [ ] 工作性质筛选功能
- [ ] 薪资范围筛选功能
- [ ] 地区筛选功能
- [ ] 关键词搜索功能
- [ ] 分页功能

### 1.2 职位详情接口 (/miniapp/position/{id})
- [ ] 获取完整职位信息
- [ ] 显示职位描述
- [ ] 显示职位要求
- [ ] 显示薪资待遇
- [ ] 显示发布时间
- [ ] 显示浏览次数

## 2. 前端功能测试

### 2.1 首页功能
- [ ] 搜索栏点击跳转
- [ ] 地区选择功能
- [ ] 排序选择（发布时间、薪资、距离）
- [ ] 薪资筛选功能
- [ ] 行业筛选功能
- [ ] 工作性质筛选功能
- [ ] 职位列表展示
- [ ] 下拉刷新功能
- [ ] 上拉加载更多
- [ ] 点击职位跳转详情

### 2.2 职位详情页
- [ ] 职位基本信息展示
- [ ] 职位标题显示
- [ ] 薪资范围显示
- [ ] 工作地址显示
- [ ] 经验要求显示
- [ ] 学历要求显示
- [ ] 工作性质显示
- [ ] 职位描述完整显示
- [ ] 职位要求完整显示
- [ ] 薪资待遇完整显示
- [ ] 发布时间显示
- [ ] 浏览次数显示
- [ ] 地图查看功能（如有坐标）

### 2.3 搜索功能
- [ ] 关键词搜索
- [ ] 搜索结果展示
- [ ] 搜索历史记录
- [ ] 搜索建议功能

## 3. 数据库更新测试

### 3.1 表结构更新
- [ ] 执行经纬度字段添加SQL
- [ ] 验证latitude字段存在
- [ ] 验证longitude字段存在
- [ ] 验证索引创建成功

## 4. 集成测试

### 4.1 完整流程测试
- [ ] 首页加载 → 筛选 → 查看详情
- [ ] 首页搜索 → 搜索页 → 查看结果
- [ ] 排序功能 → 结果正确排列
- [ ] 筛选功能 → 结果正确过滤

## 5. 性能测试

### 5.1 响应时间
- [ ] 职位列表加载时间 < 2秒
- [ ] 职位详情加载时间 < 1秒
- [ ] 筛选响应时间 < 1秒

### 5.2 数据量测试
- [ ] 大量职位数据加载
- [ ] 分页功能正常
- [ ] 内存使用合理

## 测试环境要求

1. 后端服务正常运行
2. 数据库连接正常
3. 前端开发环境配置完成
4. 测试数据准备充分

## 测试注意事项

1. 距离排序需要用户授权位置信息
2. 地图功能需要有效的经纬度坐标
3. 富文本内容需要正确解析显示
4. 图片资源需要正确加载
5. 网络异常情况的处理
