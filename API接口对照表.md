# 招聘平台API接口对照表

## 修复说明
已修复前端API请求路径与后端接口路径不匹配导致的404错误问题。

## 接口对照表

### 1. 用户认证相关 (authApi)
| 功能 | 前端方法 | 请求路径 | 后端控制器 | 状态 |
|------|----------|----------|------------|------|
| 微信授权登录 | wechatLogin | POST /miniapp/auth/wechat | MiniAppLoginController | ✅ 正确 |
| 手机号登录(微信) | phoneLoginByWechat | POST /miniapp/auth/phone/wechat | MiniAppLoginController | ✅ 正确 |
| 手机号登录(验证码) | phoneLogin | POST /miniapp/auth/phone | MiniAppLoginController | ✅ 正确 |
| 发送短信验证码 | sendSmsCode | POST /miniapp/auth/sms/send | MiniAppLoginController | ✅ 正确 |
| 用户注册 | register | POST /miniapp/auth/register | MiniAppLoginController | ✅ 正确 |
| 获取用户信息 | getUserInfo | GET /miniapp/user/{seekerId} | MiniAppController | ✅ 已修复 |

### 2. 招聘信息相关 (jobApi)
| 功能 | 前端方法 | 请求路径 | 后端控制器 | 状态 |
|------|----------|----------|------------|------|
| 获取职位列表 | getJobList | GET /miniapp/positions | MiniAppController | ✅ 已修复 |
| 获取职位详情 | getJobDetail | GET /miniapp/position/{positionId} | MiniAppController | ✅ 已修复 |
| 搜索职位 | searchJobs | GET /miniapp/positions | MiniAppController | ✅ 已修复 |
| 获取热门职位 | getHotJobs | GET /miniapp/positions/hot | MiniAppController | ✅ 已修复 |
| 获取最新职位 | getLatestJobs | GET /miniapp/positions/latest | MiniAppController | ✅ 已修复 |
| 查看联系方式 | viewContact | POST /miniapp/position/{positionId}/contact | MiniAppController | ✅ 已修复 |

### 3. 地区相关 (regionApi)
| 功能 | 前端方法 | 请求路径 | 后端控制器 | 状态 |
|------|----------|----------|------------|------|
| 获取地区列表 | getRegions | GET /miniapp/regions | MiniAppController | ✅ 已修复 |

### 4. 付费功能相关 (paymentApi)
| 功能 | 前端方法 | 请求路径 | 后端控制器 | 状态 |
|------|----------|----------|------------|------|
| 获取会员套餐 | getMemberPackages | GET /miniapp/packages | MiniAppController | ✅ 已修复 |
| 创建订单 | createOrder | POST /miniapp/order/create | MiniAppController | ✅ 已修复 |
| 微信支付 | wechatPay | POST /miniapp/pay/wechat | MiniAppController | ✅ 已修复 |
| 获取消费记录 | getUserRecords | GET /miniapp/records/{seekerId} | MiniAppController | ✅ 已修复 |
| 检查会员状态 | getMemberStatus | GET /miniapp/member/status/{seekerId} | MiniAppController | ✅ 已修复 |

## 主要修复内容

### 1. 用户信息接口
- **修复前**: `/miniapp/user/info`
- **修复后**: `/miniapp/user/{seekerId}`
- **说明**: 后端需要seekerId参数来获取特定用户信息

### 2. 招聘信息接口
- **修复前**: `/job/*` 路径
- **修复后**: `/miniapp/positions*` 路径
- **说明**: 所有招聘相关接口都在miniapp路径下

### 3. 地区信息接口
- **修复前**: `/region/*` 多个接口
- **修复后**: `/miniapp/regions` 单一接口
- **说明**: 后端通过parentCode参数来获取不同层级的地区数据

### 4. 付费功能接口
- **修复前**: `/payment/*` 路径
- **修复后**: `/miniapp/*` 路径
- **说明**: 所有付费相关接口都在miniapp路径下

## 注意事项

1. **参数传递**: 部分接口的参数传递方式有调整，请确保前端调用时传递正确的参数
2. **用户ID**: 很多接口需要seekerId参数，请确保在调用时传递当前登录用户的ID
3. **错误处理**: 建议在前端添加更详细的错误处理和日志输出
4. **测试验证**: 修复后请逐一测试各个接口确保正常工作

## 服务器配置
- **后端端口**: 7799
- **前端配置**: http://127.0.0.1:7799
- **上下文路径**: /
