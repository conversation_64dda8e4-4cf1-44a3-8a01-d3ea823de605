// 工具函数
import { getAvatarUrl, getCaseImageUrl } from "./imageConfig.js";

// 格式化时间
export const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now - date;

  // 小于1分钟
  if (diff < 60000) {
    return "刚刚";
  }

  // 小于1小时
  if (diff < 3600000) {
    return Math.floor(diff / 60000) + "分钟前";
  }

  // 小于1天
  if (diff < 86400000) {
    return Math.floor(diff / 3600000) + "小时前";
  }

  // 小于7天
  if (diff < 604800000) {
    return Math.floor(diff / 86400000) + "天前";
  }

  // 格式化为日期
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  if (year === now.getFullYear()) {
    return `${month}-${day}`;
  }

  return `${year}-${month}-${day}`;
};

// 处理图片数组
export const processImages = (imageStr, size = "medium") => {
  if (!imageStr) return [];
  return imageStr
    .split(",")
    .filter((img) => img.trim())
    .map((img) => getCaseImageUrl(img.trim(), size));
};

// 处理图片数组
export const processImage = (imageStr, size = "medium") => {
  if (!imageStr) return "";
  return getCaseImageUrl(imageStr, size);
};

// 处理头像URL
export const processAvatarUrl = (avatarUrl, size = "medium") => {
  return getAvatarUrl(avatarUrl, size);
};

// 处理标签数组
export const processTags = (tagStr) => {
  if (!tagStr) return [];
  return tagStr.split(",").filter((tag) => tag.trim());
};

// 截取文本
export const truncateText = (text, maxLength = 100) => {
  if (!text) return "";
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
};

// 去除HTML标签
export const stripHtml = (html) => {
  if (!html) return "";
  return html.replace(/<[^>]*>/g, "");
};

// 处理案例内容（去除HTML标签并截取）
export const processCaseContent = (content, maxLength = 150) => {
  const plainText = stripHtml(content);
  return truncateText(plainText, maxLength);
};

// 图片预览
export const previewImages = (images, current = 0) => {
  if (!images || images.length === 0) return;

  // 确保所有图片URL都是完整的
  const processedImages = images.map((img) => {
    // 如果传入的已经是处理过的URL数组，直接使用
    if (typeof img === "string") {
      // 检查是否已经是完整的URL
      if (
        img.startsWith("http://") ||
        img.startsWith("https://") ||
        img.startsWith("/static/")
      ) {
        return img;
      }
      // 如果不是完整URL，则进行处理
      return getCaseImageUrl(img, "large"); // 预览时使用大图
    }
    return img;
  });

  uni.previewImage({
    urls: processedImages,
    current: current,
    fail: (err) => {
      console.error("图片预览失败:", err);
      uni.showToast({
        title: "图片预览失败",
        icon: "none",
      });
    },
  });
};

// ========== 招聘相关工具函数 ==========

// 格式化薪资显示
export const formatSalary = (minSalary, maxSalary, salaryType = "month") => {
  if (!minSalary && !maxSalary) return "面议";

  const unit =
    salaryType === "month" ? "/月" : salaryType === "day" ? "/天" : "/小时";

  if (minSalary && maxSalary) {
    if (minSalary === maxSalary) {
      return `${minSalary}元${unit}`;
    }
    return `${minSalary}-${maxSalary}元${unit}`;
  }

  return `${minSalary || maxSalary}元${unit}`;
};

// 计算距离显示
export const formatDistance = (distance) => {
  if (!distance) return "";
  if (distance < 1000) {
    return `${Math.round(distance)}m`;
  }
  return `${(distance / 1000).toFixed(1)}km`;
};

// 手机号验证
export const validatePhone = (phone) => {
  const phoneReg = /^1[3-9]\d{9}$/;
  return phoneReg.test(phone);
};

// 防抖函数
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 节流函数
export const throttle = (func, limit) => {
  let inThrottle;
  return function () {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// 存储相关
export const storage = {
  set(key, value) {
    try {
      uni.setStorageSync(key, JSON.stringify(value));
    } catch (e) {
      console.error("存储失败:", e);
    }
  },

  get(key, defaultValue = null) {
    try {
      const value = uni.getStorageSync(key);
      return value ? JSON.parse(value) : defaultValue;
    } catch (e) {
      console.error("读取存储失败:", e);
      return defaultValue;
    }
  },

  remove(key) {
    try {
      uni.removeStorageSync(key);
    } catch (e) {
      console.error("删除存储失败:", e);
    }
  },

  clear() {
    try {
      uni.clearStorageSync();
    } catch (e) {
      console.error("清空存储失败:", e);
    }
  },
};

// 用户登录状态管理
export const auth = {
  // 设置token
  setToken(token) {
    try {
      // 直接存储token字符串，不进行JSON序列化
      uni.setStorageSync("token", token);
    } catch (e) {
      console.error("存储token失败:", e);
    }
  },

  // 获取token
  getToken() {
    try {
      return uni.getStorageSync("token");
    } catch (e) {
      console.error("获取token失败:", e);
      return null;
    }
  },

  // 清除token
  clearToken() {
    try {
      uni.removeStorageSync("token");
    } catch (e) {
      console.error("清除token失败:", e);
    }
  },

  // 检查是否登录
  isLoggedIn() {
    return !!this.getToken();
  },

  // 设置用户信息
  setUserInfo(userInfo) {
    storage.set("userInfo", userInfo);
  },

  // 获取用户信息
  getUserInfo() {
    return storage.get("userInfo");
  },

  // 清除用户信息
  clearUserInfo() {
    storage.remove("userInfo");
  },

  // 退出登录
  logout() {
    this.clearToken();
    this.clearUserInfo();
    // 跳转到登录页
    uni.reLaunch({
      url: "/pages/auth/login",
    });
  },
};

// 通用提示函数
export const showToast = (title, icon = "none", duration = 2000) => {
  uni.showToast({
    title,
    icon,
    duration,
  });
};

export const showLoading = (title = "加载中...") => {
  uni.showLoading({
    title,
    mask: true,
  });
};

export const hideLoading = () => {
  uni.hideLoading();
};

// 确认对话框
export const showConfirm = (content, title = "提示") => {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      success: (res) => {
        resolve(res.confirm);
      },
    });
  });
};

// 获取当前位置
export const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type: "gcj02",
      success: (res) => {
        resolve({
          latitude: res.latitude,
          longitude: res.longitude,
        });
      },
      fail: (err) => {
        console.error("获取位置失败:", err);
        reject(err);
      },
    });
  });
};
